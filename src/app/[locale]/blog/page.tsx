'use client';
import React, { useState } from 'react';
import { motion } from "framer-motion";
import { useParams } from 'next/navigation';
import { t, type Locale } from '@/lib/i18n';
import Hero from '@/components/ui/Hero';
import Card, { CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { FeatureIcons, NavigationIcons } from '@/components/ui/Icons';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// 现代化博客文章卡片组件
const ModernBlogCard = ({
  title,
  excerpt,
  date,
  tags,
  category,
  readTime,
  author,
  featured = false,
  locale
}: {
  title: string;
  excerpt: string;
  date: string;
  tags: string[];
  category: string;
  readTime: string;
  author: string;
  featured?: boolean;
  locale: Locale;
}) => (
  <motion.div
    variants={fadeInUp}
    className="group"
  >
    <Card
      className={`h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200 ${
        featured ? 'ring-2 ring-primary-200' : ''
      }`}
      padding="none"
      shadow="lg"
      border={false}
    >
      {/* 文章头部 */}
      <CardHeader className="p-6">
        <div className="flex items-start justify-between mb-3">
          <Badge variant={featured ? "primary" : "outline"} size="sm">
            {category}
          </Badge>
          {featured && (
            <Badge variant="secondary" size="sm">
              {locale === 'zh' ? '精选' : 'Featured'}
            </Badge>
          )}
        </div>
        <h3 className="text-xl font-semibold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors line-clamp-2">
          {title}
        </h3>
        <p className="text-neutral-600 text-sm leading-relaxed line-clamp-3">
          {excerpt}
        </p>
      </CardHeader>

      <CardContent className="p-6 pt-0">
        {/* 标签 */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" size="sm">
                {tag}
              </Badge>
            ))}
            {tags.length > 3 && (
              <Badge variant="outline" size="sm">
                +{tags.length - 3}
              </Badge>
            )}
          </div>
        </div>

        {/* 文章信息 */}
        <div className="flex items-center justify-between text-sm text-neutral-500 mb-4">
          <div className="flex items-center space-x-4">
            <span>{author}</span>
            <span>•</span>
            <span>{readTime}</span>
          </div>
          <span>{new Date(date).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}</span>
        </div>

        {/* 阅读按钮 */}
        <Button
          variant="outline"
          size="sm"
          fullWidth
          className="group-hover:border-primary-600 group-hover:text-primary-600"
          icon={NavigationIcons.Arrow}
          iconPosition="right"
        >
          {locale === 'zh' ? '阅读全文' : 'Read More'}
        </Button>
      </CardContent>
    </Card>
  </motion.div>
);

export default function Blog() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 文章分类
  const categories = [
    { id: 'all', name: isZh ? '全部' : 'All' },
    { id: 'ios', name: isZh ? 'iOS 开发' : 'iOS Development' },
    { id: 'frontend', name: isZh ? '前端开发' : 'Frontend' },
    { id: 'backend', name: isZh ? '后端开发' : 'Backend' },
    { id: 'design', name: isZh ? '设计' : 'Design' },
    { id: 'devops', name: isZh ? '运维' : 'DevOps' },
    { id: 'tutorial', name: isZh ? '教程' : 'Tutorial' }
  ];

  // 博客文章数据
  const posts = [
    {
      title: isZh ? 'SwiftUI 5.0 新特性完全指南' : 'Complete Guide to SwiftUI 5.0 New Features',
      excerpt: isZh
        ? '深入探索 SwiftUI 5.0 的革命性新特性，包括新的动画系统、改进的数据绑定、增强的导航功能，以及如何在实际项目中应用这些特性。'
        : 'Deep dive into SwiftUI 5.0 revolutionary new features, including the new animation system, improved data binding, enhanced navigation, and how to apply these features in real projects.',
      date: '2024-12-20',
      tags: ['SwiftUI', 'iOS', 'Animation', 'Navigation'],
      category: 'ios',
      readTime: isZh ? '12 分钟阅读' : '12 min read',
      author: isZh ? '李明' : 'Ming Li',
      featured: true
    },
    {
      title: isZh ? 'Swift 6.0 并发编程最佳实践' : 'Swift 6.0 Concurrency Best Practices',
      excerpt: isZh
        ? '全面解析 Swift 6.0 的并发编程模型，包括 async/await、Actor、TaskGroup 等新特性，以及如何构建高性能的异步 iOS 应用。'
        : 'Comprehensive analysis of Swift 6.0 concurrency model, including async/await, Actor, TaskGroup and other new features, and how to build high-performance asynchronous iOS apps.',
      date: '2024-12-18',
      tags: ['Swift', 'Concurrency', 'async/await', 'Actor'],
      category: 'ios',
      readTime: isZh ? '15 分钟阅读' : '15 min read',
      author: isZh ? '王小华' : 'Xiaohua Wang',
      featured: true
    },
    {
      title: isZh ? '用 Next.js 15 构建多语言网站完整指南' : 'Complete Guide to Building Multilingual Sites with Next.js 15',
      excerpt: isZh
        ? '深入探讨如何使用 Next.js 15 的 App Router 构建支持多语言的现代网站，包括路由配置、SEO 优化、性能提升等最佳实践。'
        : 'An in-depth exploration of building modern multilingual websites using Next.js 15 App Router, including routing configuration, SEO optimization, and performance best practices.',
      date: '2024-12-15',
      tags: ['Next.js', 'i18n', 'SEO', 'App Router'],
      category: 'frontend',
      readTime: isZh ? '8 分钟阅读' : '8 min read',
      author: isZh ? '张伟' : 'David Zhang',
      featured: false
    },
    {
      title: isZh ? 'Tailwind CSS 4 新特性深度解析' : 'Deep Dive into Tailwind CSS 4 New Features',
      excerpt: isZh
        ? '全面解析 Tailwind CSS 4 的新特性，包括原生 CSS 变量、改进的渐变系统、新的动画工具类等，让你的界面设计更加灵活。'
        : 'Comprehensive analysis of Tailwind CSS 4 new features, including native CSS variables, improved gradient system, and new animation utilities for more flexible interface design.',
      date: '2024-12-10',
      tags: ['Tailwind CSS', 'CSS', 'Design System'],
      category: 'design',
      readTime: isZh ? '6 分钟阅读' : '6 min read',
      author: isZh ? '陈美丽' : 'Bella Chen',
      featured: true
    },
    {
      title: isZh ? 'iOS 18 新特性深度解析与适配指南' : 'iOS 18 New Features Deep Dive and Adaptation Guide',
      excerpt: isZh
        ? '全面解析 iOS 18 的新特性，包括 Control Center 自定义、Lock Screen 小组件、新的隐私控制等，以及如何在现有应用中适配这些新功能。'
        : 'Comprehensive analysis of iOS 18 new features, including Control Center customization, Lock Screen widgets, new privacy controls, and how to adapt these features in existing apps.',
      date: '2024-12-12',
      tags: ['iOS 18', 'Control Center', 'Lock Screen', 'Privacy'],
      category: 'ios',
      readTime: isZh ? '10 分钟阅读' : '10 min read',
      author: isZh ? '陈志强' : 'Zhiqiang Chen',
      featured: false
    },
    {
      title: isZh ? 'SwiftUI 中的高级动画技巧' : 'Advanced Animation Techniques in SwiftUI',
      excerpt: isZh
        ? '深入学习 SwiftUI 的高级动画技巧，包括自定义转场动画、复杂的手势交互、物理动画效果，让你的 iOS 应用更加生动有趣。'
        : 'Deep dive into advanced SwiftUI animation techniques, including custom transitions, complex gesture interactions, and physics-based animations to make your iOS apps more engaging.',
      date: '2024-12-08',
      tags: ['SwiftUI', 'Animation', 'Gesture', 'Physics'],
      category: 'ios',
      readTime: isZh ? '14 分钟阅读' : '14 min read',
      author: isZh ? '李明' : 'Ming Li',
      featured: false
    },
    {
      title: isZh ? 'React 19 并发特性实战应用' : 'Practical Applications of React 19 Concurrent Features',
      excerpt: isZh
        ? '探索 React 19 的并发特性如何提升用户体验，包括 Suspense、Concurrent Rendering、Server Components 等新功能的实际应用场景。'
        : 'Explore how React 19 concurrent features enhance user experience, including practical applications of Suspense, Concurrent Rendering, and Server Components.',
      date: '2024-12-05',
      tags: ['React', 'Concurrent', 'Performance'],
      category: 'frontend',
      readTime: isZh ? '10 分钟阅读' : '10 min read',
      author: isZh ? '李小雨' : 'Rain Li',
      featured: false
    },
    {
      title: isZh ? 'Node.js 微服务架构设计模式' : 'Node.js Microservices Architecture Design Patterns',
      excerpt: isZh
        ? '详细介绍如何使用 Node.js 构建可扩展的微服务架构，包括服务发现、负载均衡、容错处理等关键设计模式。'
        : 'Detailed introduction to building scalable microservices architecture with Node.js, including service discovery, load balancing, and fault tolerance patterns.',
      date: '2024-11-28',
      tags: ['Node.js', 'Microservices', 'Architecture'],
      category: 'backend',
      readTime: isZh ? '12 分钟阅读' : '12 min read',
      author: isZh ? '王强' : 'Strong Wang',
      featured: false
    },
    {
      title: isZh ? 'Kubernetes 生产环境最佳实践' : 'Kubernetes Production Best Practices',
      excerpt: isZh
        ? '分享在生产环境中部署和管理 Kubernetes 集群的最佳实践，包括安全配置、监控告警、资源管理等关键要点。'
        : 'Share best practices for deploying and managing Kubernetes clusters in production, including security configuration, monitoring, and resource management.',
      date: '2024-11-20',
      tags: ['Kubernetes', 'DevOps', 'Production'],
      category: 'devops',
      readTime: isZh ? '15 分钟阅读' : '15 min read',
      author: isZh ? '刘志明' : 'Ming Liu',
      featured: false
    },
    {
      title: isZh ? '现代 Web 性能优化策略' : 'Modern Web Performance Optimization Strategies',
      excerpt: isZh
        ? '全面介绍现代 Web 应用的性能优化策略，从代码分割、懒加载到 CDN 配置，帮你构建更快的用户体验。'
        : 'Comprehensive guide to modern web application performance optimization strategies, from code splitting and lazy loading to CDN configuration.',
      date: '2024-11-15',
      tags: ['Performance', 'Web Vitals', 'Optimization'],
      category: 'frontend',
      readTime: isZh ? '9 分钟阅读' : '9 min read',
      author: isZh ? '李小雨' : 'Rain Li',
      featured: false
    },
    {
      title: isZh ? 'UI/UX 设计系统构建指南' : 'Building UI/UX Design Systems Guide',
      excerpt: isZh
        ? '从零开始构建一套完整的设计系统，包括颜色规范、字体系统、组件库设计等，提升团队协作效率。'
        : 'Build a complete design system from scratch, including color specifications, typography systems, and component library design to improve team collaboration.',
      date: '2024-11-10',
      tags: ['Design System', 'UI/UX', 'Figma'],
      category: 'design',
      readTime: isZh ? '11 分钟阅读' : '11 min read',
      author: isZh ? '陈美丽' : 'Bella Chen',
      featured: false
    },
    {
      title: isZh ? 'Swift Package Manager 高级用法指南' : 'Advanced Swift Package Manager Usage Guide',
      excerpt: isZh
        ? '深入学习 Swift Package Manager 的高级用法，包括创建可复用的 Swift 包、依赖管理、版本控制策略，提升 iOS 项目的模块化程度。'
        : 'Deep dive into advanced Swift Package Manager usage, including creating reusable Swift packages, dependency management, and versioning strategies to improve iOS project modularity.',
      date: '2024-11-25',
      tags: ['Swift', 'SPM', 'Package Manager', 'Modularity'],
      category: 'ios',
      readTime: isZh ? '11 分钟阅读' : '11 min read',
      author: isZh ? '王小华' : 'Xiaohua Wang',
      featured: false
    },
    {
      title: isZh ? 'Core Data 与 SwiftUI 完美结合实践' : 'Perfect Integration of Core Data and SwiftUI',
      excerpt: isZh
        ? '探索如何在 SwiftUI 应用中高效使用 Core Data，包括数据绑定、性能优化、错误处理等最佳实践，构建数据驱动的 iOS 应用。'
        : 'Explore how to efficiently use Core Data in SwiftUI apps, including data binding, performance optimization, error handling best practices for building data-driven iOS apps.',
      date: '2024-11-18',
      tags: ['Core Data', 'SwiftUI', 'Data Binding', 'Performance'],
      category: 'ios',
      readTime: isZh ? '16 分钟阅读' : '16 min read',
      author: isZh ? '陈志强' : 'Zhiqiang Chen',
      featured: false
    },
    {
      title: isZh ? 'iOS 应用架构设计模式对比分析' : 'Comparative Analysis of iOS App Architecture Patterns',
      excerpt: isZh
        ? '深入对比分析 MVC、MVP、MVVM、VIPER 等 iOS 应用架构模式，帮你选择最适合项目的架构设计，提升代码质量和可维护性。'
        : 'In-depth comparative analysis of iOS app architecture patterns like MVC, MVP, MVVM, VIPER to help you choose the best architecture for your project and improve code quality.',
      date: '2024-11-12',
      tags: ['iOS', 'Architecture', 'MVC', 'MVVM', 'VIPER'],
      category: 'ios',
      readTime: isZh ? '18 分钟阅读' : '18 min read',
      author: isZh ? '李明' : 'Ming Li',
      featured: false
    },
    {
      title: isZh ? 'TypeScript 高级类型系统实战' : 'Advanced TypeScript Type System in Practice',
      excerpt: isZh
        ? '深入学习 TypeScript 的高级类型系统，包括条件类型、映射类型、模板字面量类型等，提升代码的类型安全性。'
        : 'Deep dive into TypeScript advanced type system, including conditional types, mapped types, and template literal types for better type safety.',
      date: '2024-11-05',
      tags: ['TypeScript', 'Types', 'Advanced'],
      category: 'tutorial',
      readTime: isZh ? '13 分钟阅读' : '13 min read',
      author: isZh ? '张伟' : 'David Zhang',
      featured: false
    }
  ];

  // 根据选中的分类筛选文章
  const filteredPosts = selectedCategory === 'all'
    ? posts
    : posts.filter(post => post.category === selectedCategory);

  // 分离精选文章和普通文章
  const featuredPosts = filteredPosts.filter(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  return (
    <>
      {/* Hero 部分 */}
      <Hero
        title={isZh ? '技术博客' : 'Tech Blog'}
        subtitle={isZh
          ? '分享我们的技术实践、设计思考和行业洞察，与开发者社区一起成长'
          : 'Sharing our technical practices, design insights, and industry perspectives to grow together with the developer community'}
        align="center"
      />

      {/* 分类筛选 */}
      <section className="py-12 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-8"
          >
            <h2 className="text-2xl font-semibold text-neutral-900 mb-4">
              {isZh ? '按分类浏览' : 'Browse by Category'}
            </h2>
            <div className="flex flex-wrap justify-center gap-3">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "primary" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="transition-all duration-200"
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* 精选文章 */}
      {featuredPosts.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeInUp}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-semibold text-neutral-900 mb-4">
                {isZh ? '精选文章' : 'Featured Articles'}
              </h2>
              <p className="text-lg text-neutral-600">
                {isZh ? '我们精心挑选的优质技术内容' : 'Carefully curated quality technical content'}
              </p>
            </motion.div>

            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={staggerContainer}
              className="grid md:grid-cols-2 gap-8 mb-16"
            >
              {featuredPosts.map((post) => (
                <ModernBlogCard
                  key={post.title}
                  {...post}
                  locale={localeTyped}
                />
              ))}
            </motion.div>
          </div>
        </section>
      )}

      {/* 所有文章 */}
      <section className={`py-16 ${featuredPosts.length > 0 ? 'bg-neutral-50' : 'bg-white'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-semibold text-neutral-900 mb-4">
              {isZh ? '最新文章' : 'Latest Articles'}
            </h2>
            <p className="text-lg text-neutral-600">
              {isZh ? '探索更多技术话题和实践经验' : 'Explore more technical topics and practical experiences'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {regularPosts.map((post) => (
              <ModernBlogCard
                key={post.title}
                {...post}
                locale={localeTyped}
              />
            ))}
          </motion.div>

          {filteredPosts.length === 0 && (
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              variants={fadeInUp}
              className="text-center py-16"
            >
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-medium text-neutral-900 mb-2">
                {isZh ? '暂无相关文章' : 'No Articles Found'}
              </h3>
              <p className="text-neutral-600">
                {isZh ? '该分类的文章正在创作中，敬请期待' : 'Articles for this category are being created, stay tuned'}
              </p>
            </motion.div>
          )}
        </div>
      </section>

      {/* CTA 部分 */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-white mb-6">
              {isZh ? '想要了解更多技术内容？' : 'Want to Learn More Technical Content?'}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {isZh
                ? '关注我们的博客，获取最新的技术文章和行业洞察'
                : 'Follow our blog for the latest technical articles and industry insights'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                onClick={() => window.location.href = `/${locale}/contact`}
                icon={NavigationIcons.Arrow}
                iconPosition="right"
              >
                {isZh ? '联系我们' : 'Contact Us'}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = `/${locale}/services`}
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                {isZh ? '了解服务' : 'Our Services'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}

