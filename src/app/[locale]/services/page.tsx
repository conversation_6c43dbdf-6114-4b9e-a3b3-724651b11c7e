'use client';

import { motion } from "framer-motion";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';
// 修改 - 补充 Button 组件导入，修复运行时报错“Button is not defined” [Confirmed via mcp-feedback-enhanced]
import Button from '@/components/ui/Button';
// 新增 - 统一 Hero 组件（Apple 风格）
import Hero from '@/components/ui/Hero';
import Card, { CardContent } from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import { NavigationIcons, FeatureIcons } from '@/components/ui/Icons';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// 现代化服务卡片组件
const ModernServiceCard = ({
  title,
  description,
  features,
  icon,
  price,
  isPopular = false,
  locale
}: {
  title: string;
  description: string;
  features: string[];
  icon: string;
  price: string;
  isPopular?: boolean;
  locale: Locale;
}) => (
  <motion.div
    variants={fadeInUp}
    className="group relative"
  >
    {isPopular && (
      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
        <Badge color="primary" soft={false}>
          {locale === 'zh' ? '热门' : 'Popular'}
        </Badge>
      </div>
    )}
    <Card
      className={`h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border ${
        isPopular
          ? 'border-primary-200 ring-2 ring-primary-100'
          : 'border-neutral-200 hover:border-primary-200'
      }`}
      padding="lg"
      shadow="lg"
      border={false}
    >
      <CardContent className="p-8 text-center">
        <div className="mb-6 flex justify-center">
          <div className={`w-20 h-20 rounded-2xl transition-all duration-300 flex items-center justify-center text-4xl ${
            isPopular
              ? 'bg-primary-100 group-hover:bg-primary-200'
              : 'bg-neutral-100 group-hover:bg-primary-100'
          }`}>
            {icon}
          </div>
        </div>
        <h3 className="text-2xl font-semibold text-neutral-900 mb-4 group-hover:text-primary-600 transition-colors">
          {title}
        </h3>
        <p className="text-neutral-600 leading-relaxed mb-6">
          {description}
        </p>
        <div className="mb-6">
          <div className="text-3xl font-bold text-primary-600 mb-2">{price}</div>
          <div className="text-sm text-neutral-500">
            {locale === 'zh' ? '起步价格' : 'Starting from'}
          </div>
        </div>
        <div className="space-y-3 mb-8">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center text-sm text-neutral-600">
              <FeatureIcons.Star className="w-4 h-4 text-primary-600 mr-3 flex-shrink-0" />
              {feature}
            </div>
          ))}
        </div>
        <Button
          variant={isPopular ? "primary" : "outline"}
          size="lg"
          className="w-full"
          onClick={() => window.location.href = `/${locale}/contact`}
        >
          {locale === 'zh' ? '立即咨询' : 'Get Quote'}
        </Button>
      </CardContent>
    </Card>
  </motion.div>
);

// 技术栈卡片组件
const TechStackCard = ({ title, description, technologies, icon }: {
  title: string;
  description: string;
  technologies: string[];
  icon: string;
}) => (
  <motion.div
    variants={fadeInUp}
    className="group"
  >
    <Card
      className="h-full transition-all duration-300 hover:scale-105 hover:shadow-xl border border-neutral-200 hover:border-primary-200"
      padding="lg"
      shadow="lg"
      border={false}
    >
      <CardContent className="p-6">
        <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors">
          {title}
        </h3>
        <p className="text-neutral-600 text-sm leading-relaxed mb-4">
          {description}
        </p>
        <div className="flex flex-wrap gap-2">
          {technologies.map((tech, index) => (
            <Badge key={index} color="neutral" className="text-xs">
              {tech}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  </motion.div>
);

// 现代化流程步骤组件
const ModernProcessStep = ({ step, title, description, icon }: {
  step: number;
  title: string;
  description: string;
  icon: string;
}) => (
  <motion.div
    variants={fadeInUp}
    className="text-center group"
  >
    <div className="mb-6 flex justify-center">
      <div className="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center text-2xl group-hover:bg-primary-200 transition-colors duration-300">
        {icon}
      </div>
    </div>
    <div className="mb-4">
      <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-sm mx-auto mb-3">
        {step}
      </div>
      <h3 className="text-lg font-semibold text-neutral-900 mb-2 group-hover:text-primary-600 transition-colors">
        {title}
      </h3>
    </div>
    <p className="text-neutral-600 text-sm leading-relaxed">
      {description}
    </p>
  </motion.div>
);

export default function Services() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  // 现代化服务数据
  const mainServices = [
    {
      title: isZh ? '移动应用开发' : 'Mobile App Development',
      description: isZh
        ? '原生 iOS/Android 应用和跨平台解决方案，提供卓越的用户体验和性能表现。'
        : 'Native iOS/Android apps and cross-platform solutions, delivering exceptional user experience and performance.',
      icon: "📱",
      price: isZh ? '¥15,000' : '$2,500',
      isPopular: true,
      features: isZh
        ? ['原生 iOS/Android 开发', 'React Native 跨平台', 'Flutter 应用开发', 'UI/UX 设计', 'App Store 上架指导', '应用维护和更新']
        : ['Native iOS/Android Development', 'React Native Cross-platform', 'Flutter App Development', 'UI/UX Design', 'App Store Launch Guidance', 'App Maintenance & Updates']
    },
    {
      title: isZh ? '小程序开发' : 'Mini-Program Development',
      description: isZh
        ? '微信、支付宝、抖音等平台小程序开发，快速触达用户，降低获客成本。'
        : 'WeChat, Alipay, TikTok and other platform mini-programs, quickly reach users and reduce customer acquisition costs.',
      icon: "💻",
      price: isZh ? '¥8,000' : '$1,200',
      features: isZh
        ? ['微信小程序开发', '支付宝小程序', '抖音小程序', '多平台适配', '小程序商城', '支付集成']
        : ['WeChat Mini-Program', 'Alipay Mini-Program', 'TikTok Mini-Program', 'Multi-platform Adaptation', 'Mini-Program Store', 'Payment Integration']
    },
    {
      title: isZh ? '后端系统开发' : 'Backend System Development',
      description: isZh
        ? '可扩展的后端架构设计，支持高并发访问，确保系统稳定性和安全性。'
        : 'Scalable backend architecture design, supporting high concurrent access, ensuring system stability and security.',
      icon: "🖥️",
      price: isZh ? '¥12,000' : '$1,800',
      features: isZh
        ? ['RESTful API 设计', '微服务架构', '数据库优化', '用户认证授权', '云服务部署', '服务器维护']
        : ['RESTful API Design', 'Microservices Architecture', 'Database Optimization', 'User Authentication', 'Cloud Service Deployment', 'Server Maintenance']
    },
    {
      title: isZh ? '网站开发' : 'Website Development',
      description: isZh
        ? '响应式网站设计，SEO 优化，提升品牌形象和在线业务转化率。'
        : 'Responsive website design, SEO optimization, enhancing brand image and online business conversion rates.',
      icon: "🌐",
      price: isZh ? '¥6,000' : '$900',
      features: isZh
        ? ['响应式设计', 'SEO 优化', '性能优化', '内容管理系统', '电商功能', '多语言支持']
        : ['Responsive Design', 'SEO Optimization', 'Performance Optimization', 'Content Management System', 'E-commerce Features', 'Multi-language Support']
    }
  ];

  // 技术栈数据
  const techStacks = [
    {
      title: isZh ? '前端技术' : 'Frontend Technologies',
      description: isZh ? '现代化前端框架和工具' : 'Modern frontend frameworks and tools',
      icon: '🎨',
      technologies: ['React', 'Vue.js', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion']
    },
    {
      title: isZh ? '后端技术' : 'Backend Technologies',
      description: isZh ? '可靠的后端技术栈' : 'Reliable backend technology stack',
      icon: '⚙️',
      technologies: ['Node.js', 'Python', 'Java', 'PostgreSQL', 'Redis', 'Docker']
    },
    {
      title: isZh ? '移动开发' : 'Mobile Development',
      description: isZh ? '跨平台移动应用开发' : 'Cross-platform mobile app development',
      icon: '📱',
      technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Expo', 'Firebase']
    },
    {
      title: isZh ? '云服务' : 'Cloud Services',
      description: isZh ? '现代化云原生部署' : 'Modern cloud-native deployment',
      icon: '☁️',
      technologies: ['AWS', 'Vercel', '阿里云', 'Kubernetes', 'CI/CD', 'Terraform']
    }
  ];

  // 现代化流程步骤数据
  const processSteps = [
    {
      title: isZh ? '需求分析' : 'Requirements Analysis',
      description: isZh
        ? '深入了解您的业务需求，确定项目目标和功能范围，制定详细的项目计划。'
        : 'Deep understanding of your business needs, defining project goals and scope, creating detailed project plans.',
      icon: '🔍'
    },
    {
      title: isZh ? '设计规划' : 'Design & Planning',
      description: isZh
        ? '制定技术方案和设计原型，确保最佳用户体验和系统架构设计。'
        : 'Developing technical solutions and design prototypes, ensuring optimal user experience and system architecture.',
      icon: '🎨'
    },
    {
      title: isZh ? '开发实施' : 'Development & Implementation',
      description: isZh
        ? '按照设计方案进行开发，采用敏捷开发方法，定期汇报进度和成果。'
        : 'Development according to design plans, using agile methodology, regular progress reports and deliverables.',
      icon: '⚡'
    },
    {
      title: isZh ? '测试交付' : 'Testing & Delivery',
      description: isZh
        ? '全面测试应用功能，确保质量后进行部署和交付，提供完整的文档和培训。'
        : 'Comprehensive testing of application features, quality assurance, deployment and delivery with complete documentation and training.',
      icon: '🚀'
    }
  ];

  return (
    <>
      {/* Hero 部分 */}
      <Hero
        title={isZh ? '专业的软件开发服务' : 'Professional Software Development Services'}
        subtitle={isZh
          ? '我们提供全方位的软件开发解决方案，从移动应用到后端系统，帮助您的业务实现数字化转型。'
          : 'We provide comprehensive software development solutions, from mobile apps to backend systems, helping your business achieve digital transformation.'}
        align="center"
      />

      {/* 服务展示部分 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '我们的服务' : 'Our Services'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '提供全面的软件开发解决方案，满足您的各种数字需求，从概念到上线的全程支持'
                : 'Comprehensive software development solutions to meet all your digital needs, from concept to launch with full support'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {mainServices.map((service, index) => (
              <ModernServiceCard
                key={index}
                title={service.title}
                description={service.description}
                icon={service.icon}
                features={service.features}
                price={service.price}
                isPopular={service.isPopular}
                locale={localeTyped}
              />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 技术栈展示部分 */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '技术栈' : 'Technology Stack'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '我们使用最新的技术栈和工具，确保为您提供高质量、可扩展的解决方案'
                : 'We use the latest technology stack and tools to ensure high-quality, scalable solutions for you'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {techStacks.map((tech, index) => (
              <TechStackCard key={index} {...tech} />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 开发流程部分 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '开发流程' : 'Development Process'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '透明高效的开发流程，确保项目顺利进行和按时交付，让您全程掌控项目进度'
                : 'Transparent and efficient development process, ensuring smooth project execution and timely delivery, keeping you in control throughout'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {processSteps.map((step, index) => (
              <ModernProcessStep
                key={index}
                step={index + 1}
                title={step.title}
                description={step.description}
                icon={step.icon}
              />
            ))}
          </motion.div>
        </div>
      </section>

      {/* 服务保障部分 */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {isZh ? '服务保障' : 'Service Guarantee'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {isZh
                ? '我们承诺为您提供最优质的服务体验和技术支持'
                : 'We promise to provide you with the best service experience and technical support'}
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid md:grid-cols-3 gap-8"
          >
            {[
              {
                icon: '🛡️',
                title: isZh ? '质量保证' : 'Quality Assurance',
                description: isZh ? '严格的代码审查和测试流程，确保交付高质量的产品' : 'Strict code review and testing process to ensure high-quality product delivery'
              },
              {
                icon: '⏰',
                title: isZh ? '按时交付' : 'On-time Delivery',
                description: isZh ? '合理的项目规划和进度管理，确保项目按时完成' : 'Reasonable project planning and progress management to ensure timely completion'
              },
              {
                icon: '🔧',
                title: isZh ? '持续支持' : 'Continuous Support',
                description: isZh ? '项目交付后提供技术支持和维护服务' : 'Technical support and maintenance services after project delivery'
              }
            ].map((guarantee, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="text-center group"
              >
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {guarantee.icon}
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors">
                  {guarantee.title}
                </h3>
                <p className="text-neutral-600 leading-relaxed">
                  {guarantee.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA 部分 */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-white mb-6">
              {isZh ? '准备开始您的项目了吗？' : 'Ready to Start Your Project?'}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {isZh
                ? '联系我们获取免费咨询和项目报价，让我们为您打造专业的数字解决方案'
                : 'Contact us for free consultation and project quote, let us create professional digital solutions for you'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="secondary"
                size="lg"
                onClick={() => window.location.href = `/${locale}/contact`}
                icon={NavigationIcons.Arrow}
                iconPosition="right"
              >
                {isZh ? '免费咨询' : 'Free Consultation'}
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = `/${locale}/portfolio`}
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                {isZh ? '查看案例' : 'View Portfolio'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
}