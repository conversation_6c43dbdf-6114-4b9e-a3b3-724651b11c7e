'use client';

import { motion } from "framer-motion";
import { useState } from "react";
// import Script from "next/script";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Hero from '@/components/ui/Hero';
import {
  ContactIcons,
  FeatureIcons,
  ToolIcons
} from '@/components/ui/Icons';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// 联系方式组件
const ContactMethod = ({
  IconComponent,
  title,
  value,
  link
}: {
  IconComponent: React.ComponentType<{ className?: string }>;
  title: string;
  value: string;
  link?: string;
}) => (
  <Card hover className="group">
    <CardContent className="flex items-start">
      {/* 修改 - 图标容器使用中性浅灰背景与小圆角 [Apple HIG] */}
      <div className="p-3 bg-neutral-100 rounded-lg group-hover:bg-neutral-200 transition-colors duration-200 mr-4">
        <IconComponent className="w-6 h-6 text-primary-600" />
      </div>
      <div>
        {/* 修改 - 标题 hover 轻微中性灰变化 [Apple HIG] */}
        <CardTitle size="sm" className="mb-2 group-hover:text-neutral-900 transition-colors">
          {title}
        </CardTitle>
        {link ? (
          <a
            href={link}
            className="text-primary-600 hover:text-primary-700 hover:underline font-medium transition-colors"
          >
            {value}
          </a>
        ) : (
          <CardDescription>{value}</CardDescription>
        )}
      </div>
    </CardContent>
  </Card>
);

export default function Contact() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;

  // 表单状态
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
    company: "", // 隐形字段（honeypot）- 真实用户不会填写
  });
  const [formErrors, setFormErrors] = useState({
    name: "",
    email: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // 清除对应的错误信息
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: "",
      });
    }
  };

  // 表单验证
  const validateForm = () => {
    let valid = true;
    const newErrors = { ...formErrors };

    if (!formData.name.trim()) {
      newErrors.name = t('contact.form.errors.nameRequired', localeTyped);
      valid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = t('contact.form.errors.emailRequired', localeTyped);
      valid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = t('contact.form.errors.emailInvalid', localeTyped);
      valid = false;
    }

    if (!formData.message.trim()) {
      newErrors.message = t('contact.form.errors.messageRequired', localeTyped);
      valid = false;
    }

    setFormErrors(newErrors);
    return valid;
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);

      try {
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            language: localeTyped
          }),
        });

        if (response.ok) {
          setSubmitted(true);
          // 重置表单
          setFormData({
            name: "",
            email: "",
            phone: "",
            message: "",
            company: "",
          });

          // 5秒后隐藏成功消息
          setTimeout(() => {
            setSubmitted(false);
          }, 5000);
        } else {
          alert(localeTyped === 'zh' ? '发送失败，请稍后重试或直接联系我们的邮箱' : 'Sending failed, please try again later or contact us directly via email');
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        alert(localeTyped === 'zh' ? '网络错误，请稍后重试或直接联系我们的邮箱' : 'Network error, please try again later or contact us directly via email');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // 常见问题数据
  const faqs = [
    {
      question: t('contact.faq.questions.timeline.q', localeTyped),
      answer: t('contact.faq.questions.timeline.a', localeTyped)
    },
    {
      question: t('contact.faq.questions.pricing.q', localeTyped),
      answer: t('contact.faq.questions.pricing.a', localeTyped)
    },
    {
      question: t('contact.faq.questions.maintenance.q', localeTyped),
      answer: t('contact.faq.questions.maintenance.a', localeTyped)
    },
    {
      question: t('contact.faq.questions.modification.q', localeTyped),
      answer: t('contact.faq.questions.modification.a', localeTyped)
    }
  ];

  // 结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": localeTyped === 'zh' ? "联系我们 - 三娃软件开发工作室" : "Contact Us - Sanva Software Development Studio",
    "description": localeTyped === 'zh' ? "联系三娃软件开发工作室，获取专业的软件开发服务咨询" : "Contact Sanva Software Development Studio for professional software development services consultation",
    "url": `https://sanva.top/${locale}/contact`,
    "mainEntity": {
      "@type": "Organization",
      "name": localeTyped === 'zh' ? "三娃软件开发工作室" : "Sanva Software Development Studio",
      "email": "<EMAIL>",
      "url": "https://sanva.top",
      "contactPoint": {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "customer service",
        "areaServed": "CN",
        "availableLanguage": localeTyped === 'zh' ? "Chinese" : "English"
      }
    }
  };

      {/* 结构化数据暂时移除注入，避免构建解析差异；如需开启可用 next/script 以 JSON.stringify 注入 */}
  return (
    <>
      {/* 页面标题 - 统一 Apple 风格 Hero：白底 + 中性文案 */}
      {/* Confirmed via mcp-feedback-enhanced */}
      <Hero
        title={t('contact.title', localeTyped)}
        subtitle={t('contact.subtitle', localeTyped)}
        align="left"
      />

      {/* 联系信息和表单 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="md:flex md:space-x-8">
            {/* 联系信息 */}
            <motion.div
              className="md:w-1/3 mb-8 md:mb-0"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-2xl font-bold text-neutral-900 mb-6">{t('contact.methods.title', localeTyped)}</h2>
              <div className="space-y-6">
                <ContactMethod
                  IconComponent={ContactIcons.Email}
                  title={t('contact.methods.email', localeTyped)}
                  value="<EMAIL>"
                  link="mailto:<EMAIL>"
                />
                <ContactMethod
                  IconComponent={ToolIcons.Clock}
                  title={t('contact.methods.workTime', localeTyped)}
                  value={t('contact.methods.workHours', localeTyped)}
                />
                <ContactMethod
                  IconComponent={ContactIcons.Phone}
                  title={localeTyped === 'zh' ? '响应时间' : 'Response Time'}
                  value={localeTyped === 'zh' ? '24小时内回复' : 'Reply within 24 hours'}
                />
                <ContactMethod
                  IconComponent={FeatureIcons.Cloud}
                  title={localeTyped === 'zh' ? '服务地区' : 'Service Area'}
                  value={localeTyped === 'zh' ? '全球远程服务' : 'Global Remote Service'}
                />
              </div>

              {/* 快速联系卡片 */}
              <div className="mt-8 p-6 bg-neutral-50 rounded-xl border border-neutral-200">
                <h3 className="text-lg font-semibold text-neutral-900 mb-4">
                  {localeTyped === 'zh' ? '快速联系' : 'Quick Contact'}
                </h3>
                <p className="text-neutral-600 mb-4">
                  {localeTyped === 'zh'
                    ? '有紧急项目需求？直接发送邮件给我们，我们会在最短时间内回复您。'
                    : 'Have urgent project needs? Send us an email directly and we will reply to you as soon as possible.'}
                </p>
                <a
                  href="mailto:<EMAIL>?subject=项目咨询&body=您好，我想咨询关于软件开发的相关服务..."
                  className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  style={{ color: 'white' }}
                >
                  <ContactIcons.Email className="w-4 h-4 mr-2 text-white" />
                  <span className="text-white">{localeTyped === 'zh' ? '立即发送邮件' : 'Send Email Now'}</span>
                </a>
              </div>

            </motion.div>

            {/* 联系表单 */}
            <motion.div
              className="md:w-2/3"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="p-6 rounded-2xl mb-6 shadow-sm border border-neutral-200/70 bg-primary-600">
                <h2 className="text-2xl font-bold text-white mb-2">{t('contact.form.title', localeTyped)}</h2>
                <p className="text-primary-100">{t('contact.form.subtitle', localeTyped)}</p>
              </div>

              {submitted ? (
                <motion.div
                  className="bg-gradient-to-r from-green-400 to-green-500 text-white p-6 rounded-lg shadow-lg"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-lg font-bold">{t('contact.form.success.title', localeTyped)}</h3>
                      <p className="mt-1 text-green-100">
                        {t('contact.form.success.message', localeTyped)}
                      </p>
                      <p className="mt-1 text-sm text-green-200">
                        {t('contact.form.success.urgent', localeTyped)}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6" aria-describedby="contact-form-desc">
                  <div id="contact-form-desc" className="sr-only">
                    {localeTyped === 'zh' ? '联系表单，所有标有星号的字段为必填项。' : 'Contact form, all fields marked with an asterisk are required.'}
                  </div>
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
                      {t('contact.form.name', localeTyped)} <span className="text-red-500">{t('contact.form.required', localeTyped)}</span>
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      aria-invalid={!!formErrors.name}
                      aria-describedby={formErrors.name ? 'name-error' : undefined}
                      className={`w-full px-4 py-2 border rounded-md input-elevate focus:outline-none focus:ring-2 focus:ring-primary-300 ${
                        formErrors.name ? "border-red-500" : "border-neutral-300"
                      }`}
                    />
                    {formErrors.name && (
                      <p id="name-error" className="mt-1 text-sm text-red-500" role="alert">{formErrors.name}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1">
                      {t('contact.form.email', localeTyped)} <span className="text-red-500">{t('contact.form.required', localeTyped)}</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      aria-invalid={!!formErrors.email}
                      aria-describedby={formErrors.email ? 'email-error' : undefined}
                      className={`w-full px-4 py-2 border rounded-md input-elevate focus:outline-none focus:ring-2 focus:ring-primary-300 ${
                        formErrors.email ? "border-red-500" : "border-neutral-300"
                      }`}
                    />
                    {formErrors.email && (
                      <p id="email-error" className="mt-1 text-sm text-red-500" role="alert">{formErrors.email}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-neutral-700 mb-1">
                      {t('contact.form.phone', localeTyped)}
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      aria-invalid="false"
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md input-elevate focus:outline-none focus:ring-2 focus:ring-primary-300"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-neutral-700 mb-1">
                      {t('contact.form.message', localeTyped)} <span className="text-red-500">{t('contact.form.required', localeTyped)}</span>
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={5}
                      value={formData.message}
                      onChange={handleInputChange}
                      aria-invalid={!!formErrors.message}
                      aria-describedby={formErrors.message ? 'message-error' : undefined}
                      className={`w-full px-4 py-2 border rounded-md input-elevate focus:outline-none focus:ring-2 focus:ring-primary-300 ${
                        formErrors.message ? "border-red-500" : "border-neutral-300"
                      }`}
                    ></textarea>
                    {formErrors.message && (
                      <p id="message-error" className="mt-1 text-sm text-red-500" role="alert">{formErrors.message}</p>

                    )}

                    {/* 隐形 honeypot 字段：反垃圾机器人，真实用户看不到 - Confirmed via mcp-feedback-enhanced */}
                    <div className="hidden" aria-hidden="true">
                      <label htmlFor="company">Company</label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        tabIndex={-1}
                        autoComplete="off"
                      />
                    </div>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full md:w-auto px-8 py-4 bg-primary-600 text-white font-semibold text-lg rounded-xl hover:bg-primary-700 focus:outline-none focus:ring-4 focus:ring-primary-300 focus:ring-offset-2 ring-offset-white dark:ring-offset-neutral-900 transition-colors duration-200 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {localeTyped === 'zh' ? '提交中...' : 'Submitting...'}
                        </span>
                      ) : (
                        t('contact.form.send', localeTyped)
                      )}
                    </button>
                  </div>
                </form>
              )}
            </motion.div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">{t('contact.faq.title', localeTyped)}</h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {t('contact.faq.subtitle', localeTyped)}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8">
            {faqs.map((item, index) => (
              <motion.div
                key={index}
                className="bg-white p-8 rounded-xl shadow-lg border border-neutral-200 hover:shadow-xl transition-all duration-300 group"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUp}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-primary-200 transition-colors">
                    <span className="text-primary-600 font-bold">Q</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors">{item.question}</h3>
                    <p className="text-neutral-600 leading-relaxed">{item.answer}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* 更多问题 CTA */}
          <motion.div
            className="text-center mt-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <p className="text-lg text-neutral-600 mb-6">
              {localeTyped === 'zh' ? '还有其他问题？' : 'Have more questions?'}
            </p>
            <a
              href="mailto:<EMAIL>?subject=更多问题咨询"
              className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              style={{ color: 'white' }}
            >
              <ContactIcons.Email className="w-5 h-5 mr-2 text-white" />
              <span className="text-white">{localeTyped === 'zh' ? '联系我们' : 'Contact Us'}</span>
            </a>
          </motion.div>
        </div>
      </section>

      {/* 服务承诺部分 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-semibold text-neutral-900 mb-6">
              {localeTyped === 'zh' ? '我们的承诺' : 'Our Promise'}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              {localeTyped === 'zh'
                ? '我们承诺为每一位客户提供最优质的服务体验'
                : 'We promise to provide the best service experience for every client'}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: '⚡',
                title: localeTyped === 'zh' ? '快速响应' : 'Quick Response',
                description: localeTyped === 'zh' ? '24小时内回复您的咨询，紧急项目可提供即时沟通' : '24-hour response to your inquiries, instant communication available for urgent projects'
              },
              {
                icon: '🎯',
                title: localeTyped === 'zh' ? '专业建议' : 'Professional Advice',
                description: localeTyped === 'zh' ? '基于丰富经验提供最适合的技术方案和商业建议' : 'Provide the most suitable technical solutions and business advice based on rich experience'
              },
              {
                icon: '🤝',
                title: localeTyped === 'zh' ? '长期合作' : 'Long-term Partnership',
                description: localeTyped === 'zh' ? '不仅是开发服务商，更是您数字化转型的长期伙伴' : 'Not just a development service provider, but your long-term partner in digital transformation'
              }
            ].map((promise, index) => (
              <motion.div
                key={index}
                className="text-center group"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={fadeInUp}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {promise.icon}
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-3 group-hover:text-primary-600 transition-colors">
                  {promise.title}
                </h3>
                <p className="text-neutral-600 leading-relaxed">
                  {promise.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}